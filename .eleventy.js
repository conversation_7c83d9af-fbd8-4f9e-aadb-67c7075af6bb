module.exports = function (eleventyConfig) {
  // TailwindCSS plugin (optional, for auto building)
  try {
    eleventyConfig.addPlugin(require("eleventy-plugin-tailwindcss"));
  } catch (e) {}

  // Add date filter
  eleventyConfig.addFilter("date", function (date, format) {
    let d;
    if (date === "now") {
      d = new Date();
    } else {
      d = new Date(date);
    }
    if (format === "yyyy") {
      return d.getFullYear().toString();
    }
    return d.toLocaleDateString();
  });

  // Passthrough for styles
  eleventyConfig.addPassthroughCopy({ "src/styles/main.css": "styles.css" });

  // Watch Tailwind CSS file for changes
  eleventyConfig.addWatchTarget("src/styles/main.css");

  // Set input/output
  return {
    dir: {
      input: "src",
      includes: "_includes",
      output: "_site",
    },
    markdownTemplateEngine: "njk",
    htmlTemplateEngine: "njk",
  };
};
