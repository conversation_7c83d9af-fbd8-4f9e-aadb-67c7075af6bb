<!DOCTYPE html>
<html lang="en" class="h-full bg-light-grey text-charcoal-grey antialiased">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact</title>
    <link rel="stylesheet" href="/styles.css" />
    <link rel="icon" href="/favicon.ico" />
  </head>
  <body class="h-full flex flex-col font-body">
    <!-- Header -->
    <header class="bg-sanrado-blue text-white shadow-lg sticky top-0 z-50">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex-shrink-0">
            <a
              href="/"
              class="text-xl sm:text-2xl font-bold font-sans tracking-tight hover:text-light-blue transition-colors duration-200"
            >
              Sanrado
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="hidden md:block">
            <ul class="flex items-center space-x-8">
              <li>
                <a
                  href="/"
                  class="hover:text-light-blue font-medium transition-colors duration-200 "
                  >Home</a
                >
              </li>
              <li>
                <a
                  href="/services/"
                  class="hover:text-light-blue font-medium transition-colors duration-200 "
                  >Services</a
                >
              </li>
              <li>
                <a
                  href="/projects/"
                  class="hover:text-light-blue font-medium transition-colors duration-200 "
                  >Projects</a
                >
              </li>
              <li>
                <a
                  href="/about/"
                  class="hover:text-light-blue font-medium transition-colors duration-200 "
                  >About</a
                >
              </li>
              <li>
                <a
                  href="/contact/"
                  class="hover:text-light-blue font-medium transition-colors duration-200 border-b-2 border-light-blue"
                  >Contact</a
                >
              </li>
            </ul>
          </nav>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button
              type="button"
              id="mobile-menu-button"
              class="text-white hover:text-light-blue focus:outline-none focus:text-light-blue transition-colors duration-200"
              aria-expanded="false"
            >
              <span class="sr-only">Open main menu</span>
              <!-- Hamburger icon -->
              <svg
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="md:hidden" id="mobile-menu" style="display: none">
          <div class="px-2 pt-2 pb-3 space-y-1 border-t border-sanrado-blue/20">
            <a
              href="/"
              class="block px-3 py-2 rounded-md text-base font-medium hover:bg-sanrado-blue/20 transition-colors duration-200 "
              >Home</a
            >
            <a
              href="/services/"
              class="block px-3 py-2 rounded-md text-base font-medium hover:bg-sanrado-blue/20 transition-colors duration-200 "
              >Services</a
            >
            <a
              href="/projects/"
              class="block px-3 py-2 rounded-md text-base font-medium hover:bg-sanrado-blue/20 transition-colors duration-200 "
              >Projects</a
            >
            <a
              href="/about/"
              class="block px-3 py-2 rounded-md text-base font-medium hover:bg-sanrado-blue/20 transition-colors duration-200 "
              >About</a
            >
            <a
              href="/contact/"
              class="block px-3 py-2 rounded-md text-base font-medium hover:bg-sanrado-blue/20 transition-colors duration-200 bg-sanrado-blue/30"
              >Contact</a
            >
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <h1>Contact Us</h1>
<p>Want to work with us or have a question?</p>
<p>Email us at <a href="mailto:<EMAIL>"><EMAIL></a></p>

    </main>

    <!-- Footer -->
    <footer class="bg-charcoal-grey text-light-grey mt-auto">
      <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div
          class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"
        >
          <!-- Footer Logo/Brand -->
          <div class="flex items-center">
            <span class="text-lg font-bold font-sans tracking-tight"
              >Sanrado</span
            >
          </div>

          <!-- Footer Links (optional for future expansion) -->
          <div class="flex space-x-6 text-sm">
            <a
              href="/about/"
              class="hover:text-white transition-colors duration-200"
              >About</a
            >
            <a
              href="/services/"
              class="hover:text-white transition-colors duration-200"
              >Services</a
            >
            <a
              href="/contact/"
              class="hover:text-white transition-colors duration-200"
              >Contact</a
            >
          </div>

          <!-- Copyright -->
          <div class="text-sm text-center md:text-right">
            &copy; 2025 Sanrado. All rights reserved.
          </div>
        </div>
      </div>
    </footer>

    <!-- Mobile Menu Toggle Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const mobileMenuButton = document.getElementById("mobile-menu-button");
        const mobileMenu = document.getElementById("mobile-menu");

        if (mobileMenuButton && mobileMenu) {
          mobileMenuButton.addEventListener("click", function () {
            const isOpen = mobileMenu.style.display !== "none";
            mobileMenu.style.display = isOpen ? "none" : "block";
            mobileMenuButton.setAttribute("aria-expanded", !isOpen);
          });
        }
      });
    </script>
  </body>
</html>
