@tailwind base;
@tailwind components;
@tailwind utilities;

/* Test to see if Tail<PERSON> is working */
.test-class {
  color: red;
}

/* Custom Components */
@layer components {
  /* Hero Section Styles */
  .hero-section {
    @apply bg-gradient-to-br from-sanrado-blue to-accent-blue text-white py-20 px-4 sm:px-6 lg:px-8;
  }

  .hero-content {
    @apply max-w-4xl mx-auto text-center;
  }

  .hero-title {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 font-sans leading-tight;
  }

  .hero-subtitle {
    @apply text-lg sm:text-xl lg:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed;
  }

  .hero-cta {
    @apply flex flex-col sm:flex-row gap-4 justify-center items-center;
  }

  .hero-image {
    @apply mt-16 flex justify-center;
  }

  .container-graphic {
    @apply relative;
  }

  .container-box {
    @apply rounded-lg shadow-lg font-bold text-center flex items-center justify-center;
  }

  .primary-container {
    @apply bg-white text-sanrado-blue w-32 h-20 text-lg relative z-10;
  }

  .secondary-container {
    @apply bg-light-blue w-24 h-16 absolute -top-2 -right-2 z-0;
  }

  /* Button Styles */
  .btn-primary {
    @apply bg-white text-sanrado-blue px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200 shadow-lg;
  }

  .btn-secondary {
    @apply border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-sanrado-blue transition-colors duration-200;
  }

  /* Solutions Section */
  .solutions-section {
    @apply py-20 px-4 sm:px-6 lg:px-8 bg-light-grey;
  }

  .solutions-header {
    @apply text-center mb-16 max-w-3xl mx-auto;
  }

  .solutions-header h2 {
    @apply text-3xl sm:text-4xl font-bold text-charcoal-grey mb-4 font-sans;
  }

  .solutions-header p {
    @apply text-lg text-gray-600;
  }

  .solutions-grid {
    @apply max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8;
  }

  .solution-card {
    @apply bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 cursor-pointer;
  }

  .large-card {
    @apply md:col-span-2 lg:col-span-1;
  }

  .card-content {
    @apply p-6;
  }

  .card-content h3 {
    @apply text-xl font-bold text-charcoal-grey mb-3 font-sans;
  }

  .card-content p {
    @apply text-gray-600 leading-relaxed;
  }

  .card-image {
    @apply h-48 bg-gradient-to-br;
  }

  .cloud-bg {
    @apply from-light-blue to-accent-blue;
  }

  .analytics-bg {
    @apply from-muted-teal to-ai-green;
  }

  .ai-bg {
    @apply from-ai-green to-muted-teal;
  }

  .dev-bg {
    @apply from-sanrado-blue to-charcoal-grey;
  }

  /* Services Section */
  .services-section {
    @apply py-20 px-4 sm:px-6 lg:px-8 bg-white;
  }

  .services-header {
    @apply text-center mb-16 max-w-4xl mx-auto;
  }

  .section-label {
    @apply text-sm font-semibold text-accent-blue uppercase tracking-wider mb-4 block;
  }

  .services-header h2 {
    @apply text-3xl sm:text-4xl font-bold text-charcoal-grey mb-6 font-sans;
  }

  .services-header p {
    @apply text-lg text-gray-600 leading-relaxed;
  }

  .services-grid {
    @apply max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8;
  }

  .service-item {
    @apply text-center p-6 rounded-xl hover:bg-light-grey transition-colors duration-200;
  }

  .service-icon {
    @apply mb-4 flex justify-center;
  }

  .icon-circle {
    @apply w-16 h-16 bg-gradient-to-br from-sanrado-blue to-accent-blue rounded-full flex items-center justify-center text-2xl;
  }

  .service-item h4 {
    @apply text-lg font-bold text-charcoal-grey mb-3 font-sans;
  }

  .service-item p {
    @apply text-gray-600 text-sm leading-relaxed;
  }
}

/* Custom Utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-sanrado-blue to-accent-blue bg-clip-text text-transparent;
  }
}
