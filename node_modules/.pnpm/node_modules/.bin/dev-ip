#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules/dev-ip/lib/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules/dev-ip/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules/dev-ip/lib/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules/dev-ip/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/dev-ip@1.0.1/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../dev-ip/lib/dev-ip.js" "$@"
else
  exec node  "$basedir/../dev-ip/lib/dev-ip.js" "$@"
fi
