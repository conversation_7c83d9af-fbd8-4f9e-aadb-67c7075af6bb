#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules/liquidjs/bin/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules/liquidjs/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules/liquidjs/bin/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules/liquidjs/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/liquidjs@10.21.1/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../liquidjs/bin/liquid.js" "$@"
else
  exec node  "$basedir/../liquidjs/bin/liquid.js" "$@"
fi
