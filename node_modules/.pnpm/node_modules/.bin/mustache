#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules/mustache/bin/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules/mustache/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules/mustache/bin/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules/mustache/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/mustache@4.2.0/node_modules:/Users/<USER>/Sites/11ty-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../mustache/bin/mustache" "$@"
else
  exec node  "$basedir/../mustache/bin/mustache" "$@"
fi
